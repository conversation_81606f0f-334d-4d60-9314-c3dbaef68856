#!/usr/bin/env python3

import requests
from sentence_transformers import SentenceTransformer
import json

# Load the model (384 dimensions) - should use cached version
model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2', cache_folder='/home/<USER>/.cache/huggingface/hub')

# Focus on the most likely candidates based on the hint
quotes = [
    # The hint mentioned "The future is not set" - try the exact script version
    "The unknown future rolls toward us. I face it, for the first time, with a sense of hope, because if a machine, a Terminator, can learn the value of human life, maybe we can too.",
    
    # Try the exact ending quote from Terminator 2
    "I know now why you cry. But it's something I can never do.",
    
    # Try the exact Kyle Reese quote from Terminator 1
    "Listen, and understand! That Terminator is out there! It can't be bargained with. It can't be reasoned with. It doesn't feel pity, or remorse, or fear. And it absolutely will not stop, ever, until you are dead!",
]

def test_embedding(quote):
    """Generate embedding and test it against the server"""
    print(f"\nTesting quote: '{quote}'")
    
    # Generate embedding
    embedding = model.encode([quote])[0]
    
    # Convert to list of floats
    embedding_list = embedding.tolist()
    
    # Send to server using the correct endpoint and format
    try:
        response = requests.post(
            'https://time-lore.pbctf.live/search',
            json={'embedding': embedding_list},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:1000]}...")
        
        # Check if we found something interesting
        if 'pbctf' in response.text.lower() or 'flag' in response.text.lower():
            print("*** POTENTIAL FLAG FOUND! ***")
            print(response.text)
            return True
            
    except Exception as e:
        print(f"Error: {e}")
    
    return False

if __name__ == "__main__":
    print("Testing specific time travel quotes...")
    
    for quote in quotes:
        if test_embedding(quote):
            break
        
    print("\nDone!")
