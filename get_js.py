#!/usr/bin/env python3

import requests
import re

def get_javascript():
    """Get the JavaScript to understand form submission"""
    try:
        response = requests.get('https://time-lore.pbctf.live')
        html = response.text
        
        # Look for JavaScript with form handling
        script_matches = re.findall(r'<script[^>]*>(.*?)</script>', html, re.DOTALL | re.IGNORECASE)
        for i, script in enumerate(script_matches):
            if 'searchForm' in script or 'fetch' in script or 'XMLHttpRequest' in script:
                print(f"\nScript {i+1}:")
                print(script)
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    get_javascript()
