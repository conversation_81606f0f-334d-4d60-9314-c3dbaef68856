#!/usr/bin/env python3

import requests
import re

def check_form():
    """Check the HTML form to understand the expected endpoint and method"""
    try:
        response = requests.get('https://time-lore.pbctf.live')
        print(f"Status: {response.status_code}")

        html = response.text

        # Look for form tags
        form_matches = re.findall(r'<form[^>]*>(.*?)</form>', html, re.DOTALL | re.IGNORECASE)
        print(f"Found {len(form_matches)} forms")

        # Look for form attributes
        form_tags = re.findall(r'<form[^>]*>', html, re.IGNORECASE)
        for i, form_tag in enumerate(form_tags):
            print(f"\nForm {i+1}: {form_tag}")

            # Extract action and method
            action_match = re.search(r'action=["\']([^"\']*)["\']', form_tag, re.IGNORECASE)
            method_match = re.search(r'method=["\']([^"\']*)["\']', form_tag, re.IGNORECASE)

            print(f"  Action: {action_match.group(1) if action_match else 'Not specified'}")
            print(f"  Method: {method_match.group(1) if method_match else 'GET (default)'}")

        # Look for input fields
        input_matches = re.findall(r'<input[^>]*>', html, re.IGNORECASE)
        for inp in input_matches:
            print(f"  Input: {inp}")

        # Look for textarea fields
        textarea_matches = re.findall(r'<textarea[^>]*>', html, re.IGNORECASE)
        for ta in textarea_matches:
            print(f"  Textarea: {ta}")

        # Look for JavaScript with fetch or XHR
        script_matches = re.findall(r'<script[^>]*>(.*?)</script>', html, re.DOTALL | re.IGNORECASE)
        for script in script_matches:
            if 'fetch' in script.lower() or 'xmlhttprequest' in script.lower():
                print(f"\nFound JavaScript with fetch/XHR:")
                print(script[:500] + "..." if len(script) > 500 else script)

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_form()
