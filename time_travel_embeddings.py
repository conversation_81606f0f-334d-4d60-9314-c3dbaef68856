#!/usr/bin/env python3

import requests
from sentence_transformers import SentenceTransformer
import json

# Load the model (384 dimensions)
model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')

# Famous time travel quotes - ACTUAL SCRIPT VERSIONS (longer, less iconic)
quotes = [
    # Terminator 2 - actual script version is longer
    "The future is not set. There is no fate but what we make for ourselves. I wish I could believe that.",
    "The future is not set. There is no fate but what we make.",
    "The unknown future rolls toward us. I face it for the first time with a sense of hope.",
    "The unknown future rolls toward us. I face it, for the first time, with a sense of hope, because if a machine, a Terminator, can learn the value of human life, maybe we can too.",

    # Back to the Future - actual versions
    "Roads? Where we're going, we don't need roads.",
    "Where we're going, we don't need roads.",
    "This is heavy, Doc. There's that word again. Heavy. Why are things so heavy in the future? Is there a problem with the Earth's gravitational pull?",
    "Great Scott! Do you realize what this means? It means that this damn thing doesn't work at all!",
    "Your future is whatever you make it, so make it a good one.",
    "Your future is whatever you make it. So make it a good one, both of you.",

    # Other variations to try
    "I'll be back.",
    "Come with me if you want to live.",
    "Listen, and understand. That Terminator is out there. It can't be bargained with. It can't be reasoned with. It doesn't feel pity, or remorse, or fear. And it absolutely will not stop, ever, until you are dead.",
    "I need your clothes, your boots and your motorcycle.",
    "Hasta la vista, baby.",

    # Try some technical terms
    "Flux capacitor",
    "1.21 gigawatts",
    "Time circuits on",
    "Temporal displacement",
    "Bootstrap paradox",
    "Causal loop",
    "Grandfather paradox",
]

def test_embedding(quote):
    """Generate embedding and test it against the server"""
    print(f"\nTesting quote: '{quote}'")

    # Generate embedding
    embedding = model.encode([quote])[0]

    # Convert to comma-separated string
    embedding_str = ','.join(map(str, embedding.tolist()))

    # Send to server
    try:
        response = requests.post(
            'https://time-lore.pbctf.live',
            data={'embedding': embedding_str},
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        
        # Check if we found something interesting
        if 'pbctf' in response.text.lower() or 'flag' in response.text.lower():
            print("*** POTENTIAL FLAG FOUND! ***")
            print(response.text)
            return True
            
    except Exception as e:
        print(f"Error: {e}")
    
    return False

def test_simple_request():
    """Test what the server expects"""
    try:
        # Try a simple GET first
        response = requests.get('https://time-lore.pbctf.live')
        print(f"GET Status: {response.status_code}")
        print(f"GET Response: {response.text[:1000]}")

        # Try POST with empty data
        response = requests.post('https://time-lore.pbctf.live', data={})
        print(f"POST Status: {response.status_code}")
        print(f"POST Response: {response.text[:500]}")

        # Try POST with simple embedding
        response = requests.post('https://time-lore.pbctf.live', data={'embedding': '0.1,0.2,0.3'})
        print(f"POST with embedding Status: {response.status_code}")
        print(f"POST with embedding Response: {response.text[:500]}")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("Testing server endpoints...")
    test_simple_request()

    print("\nTesting famous time travel quotes...")

    for quote in quotes:
        if test_embedding(quote):
            break

    print("\nDone!")
