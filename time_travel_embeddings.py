#!/usr/bin/env python3

import requests
from sentence_transformers import SentenceTransformer
import json

# Load the model (384 dimensions)
model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')

# Famous time travel quotes from sci-fi movies/shows
quotes = [
    "The future is not set. There is no fate but what we make for ourselves.",
    "Roads? Where we're going, we don't need roads.",
    "Great Scott!",
    "I'll be back.",
    "Come with me if you want to live.",
    "The future is not set",
    "There is no fate but what we make",
    "Hasta la vista, baby",
    "I need your clothes, your boots and your motorcycle",
    "Listen, and understand. That terminator is out there.",
    "The unknown future rolls toward us",
    "Every second counts",
    "Time travel is possible",
    "The timeline must be preserved",
    "Flux capacitor",
    "1.21 gigawatts",
    "This is heavy, <PERSON>",
    "Nobody calls me chicken",
    "Your future is whatever you make it",
    "Time circuits on",
    "Temporal displacement",
    "Bootstrap paradox",
    "Causal loop",
    "Grandfather paradox",
    "The past cannot be changed",
    "Wibbly wobbly timey wimey",
    "Don't blink",
    "The angels have the phone box",
    "Allons-y!",
    "Geronimo!",
]

def test_embedding(quote):
    """Generate embedding and test it against the server"""
    print(f"\nTesting quote: '{quote}'")
    
    # Generate embedding
    embedding = model.encode([quote])[0]
    
    # Convert to comma-separated string
    embedding_str = ','.join(map(str, embedding.tolist()))
    
    # Send to server
    try:
        response = requests.post(
            'https://time-lore.pbctf.live',
            data={'embedding': embedding_str},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        
        # Check if we found something interesting
        if 'pbctf' in response.text.lower() or 'flag' in response.text.lower():
            print("*** POTENTIAL FLAG FOUND! ***")
            print(response.text)
            return True
            
    except Exception as e:
        print(f"Error: {e}")
    
    return False

if __name__ == "__main__":
    print("Testing famous time travel quotes...")
    
    for quote in quotes:
        if test_embedding(quote):
            break
        
    print("\nDone!")
