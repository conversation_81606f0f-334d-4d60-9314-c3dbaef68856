#!/usr/bin/env python3

import requests
from sentence_transformers import SentenceTransformer
import json

# Load the model (384 dimensions)
model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')

# Famous time travel quotes - ACTUAL SCRIPT VERSIONS (longer, less iconic)
quotes = [
    # Try the exact Terminator 2 ending monologue - the actual script version
    "The unknown future rolls toward us. I face it, for the first time, with a sense of hope, because if a machine, a Terminator, can learn the value of human life, maybe we can too.",
    "I know now why you cry. But it's something I can never do.",
    "I cannot self-terminate. You must lower me into the steel.",
    "Now I know why you cry, but it's something I can never do.",

    # Try other exact script quotes
    "Listen, and understand! That Terminator is out there! It can't be bargained with. It can't be reasoned with. It doesn't feel pity, or remorse, or fear. And it absolutely will not stop, ever, until you are dead!",
    "Come with me if you want to live.",
    "I'll be back.",
    "Hasta la vista, baby.",
    "I need your clothes, your boots and your motorcycle.",

    # Back to the Future exact script versions
    "Roads? Where we're going, we don't need roads.",
    "Great Scott!",
    "This is heavy, <PERSON>.",
    "Nobody calls me chicken.",
    "Your future is whatever you make it, so make it a good one.",

    # Try some other famous sci-fi time travel quotes
    "The future is not set. There is no fate but what we make for ourselves.",
    "Time is not the boss of you.",
    "People assume that time is a strict progression of cause to effect, but actually from a non-linear, non-subjective viewpoint, it's more like a big ball of wibbly-wobbly, timey-wimey stuff.",
    "Don't blink. Blink and you're dead.",
    "The angels have the phone box.",

    # Technical terms
    "Flux capacitor",
    "1.21 gigawatts",
    "Bootstrap paradox",
    "Causal loop",
    "Grandfather paradox",
    "Temporal displacement",
    "Time circuits on",
]

def test_embedding(quote):
    """Generate embedding and test it against the server"""
    print(f"\nTesting quote: '{quote}'")

    # Generate embedding
    embedding = model.encode([quote])[0]

    # Convert to list of floats
    embedding_list = embedding.tolist()

    # Send to server using the correct endpoint and format
    try:
        response = requests.post(
            'https://time-lore.pbctf.live/search',
            json={'embedding': embedding_list},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        
        # Check if we found something interesting
        if 'pbctf' in response.text.lower() or 'flag' in response.text.lower():
            print("*** POTENTIAL FLAG FOUND! ***")
            print(response.text)
            return True
            
    except Exception as e:
        print(f"Error: {e}")
    
    return False

def test_simple_request():
    """Test what the server expects"""
    try:
        # Try a simple GET first
        response = requests.get('https://time-lore.pbctf.live')
        print(f"GET Status: {response.status_code}")
        print(f"GET Response: {response.text[:1000]}")

        # Try POST with empty data
        response = requests.post('https://time-lore.pbctf.live', data={})
        print(f"POST Status: {response.status_code}")
        print(f"POST Response: {response.text[:500]}")

        # Try POST with simple embedding
        response = requests.post('https://time-lore.pbctf.live', data={'embedding': '0.1,0.2,0.3'})
        print(f"POST with embedding Status: {response.status_code}")
        print(f"POST with embedding Response: {response.text[:500]}")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("Testing server endpoints...")
    test_simple_request()

    print("\nTesting famous time travel quotes...")

    for quote in quotes:
        if test_embedding(quote):
            break

    print("\nDone!")
